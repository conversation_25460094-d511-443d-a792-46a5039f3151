#!/bin/make

CGDS := clientgame-developer-site
DOCKER_DIR := ./docker/${CGDS}
DOCKER_COMPOSE := -f ${DOCKER_DIR}/docker-compose.yml
DOCKER_COMPOSE_LOCAL_DB := -f ${DOCKER_DIR}/docker-compose.local-db.yml
HOME_IN_CONTAINER := /home/<USER>/${CGDS}

ifeq ($(shell uname -m),arm64)
# apple silicon e.g. m1 mac
init:
	[ -e "${DOCKER_DIR}/.env" ] || cp -f ${DOCKER_DIR}/.env.apple-silicon ${DOCKER_DIR}/.env
	[ -e ".env" ] || cp -f .env.local .env
else
# not apple silicon e.g. windows(wsl), intel mac
init:
	[ -e "${DOCKER_DIR}/.env" ] || cp -f ${DOCKER_DIR}/.env.macintosh ${DOCKER_DIR}/.env
	[ -e ".env" ] || cp -f .env.local .env
endif

reset-env:
	rm -rf ${DOCKER_DIR}/.env
	rm -rf .env
install:
	make init up composer migrate npm-install npm-dev
build:
	docker-compose ${DOCKER_COMPOSE} build --no-cache
up:
	make init
	docker-compose ${DOCKER_COMPOSE} up -d
up-build:
	make init
	docker-compose ${DOCKER_COMPOSE} up -d --build
down:
	docker-compose ${DOCKER_COMPOSE} down
down-volume:
	docker-compose ${DOCKER_COMPOSE} down -v
stop:
	docker-compose ${DOCKER_COMPOSE} stop
ps:
	docker-compose ${DOCKER_COMPOSE} ps
balus:
	docker-compose ${DOCKER_COMPOSE} down --rmi all --volumes --remove-orphans
	sudo rm -rf ./docker/${CGDS}/volumes/db
	sudo rm -rf ./docker/${CGDS}/volumes/db2
	sudo rm -rf ./docker/${CGDS}/volumes/db3
re-build: balus build

composer:
	docker-compose ${DOCKER_COMPOSE} exec ${CGDS} bash -c "cd ${HOME_IN_CONTAINER} && composer install"

composer-dump:
	docker-compose ${DOCKER_COMPOSE} exec ${CGDS} bash -c "cd ${HOME_IN_CONTAINER} && composer dump-autoload"

migrate:
	docker-compose ${DOCKER_COMPOSE} exec ${CGDS} bash -c "cd ${HOME_IN_CONTAINER} && php artisan migrate --env=testing"

npm-install:
	docker-compose ${DOCKER_COMPOSE} exec ${CGDS} bash -c "cd ${HOME_IN_CONTAINER} && npm install"

npm-dev:
	docker-compose ${DOCKER_COMPOSE} exec ${CGDS} bash -c "cd ${HOME_IN_CONTAINER} && npm run dev"

cgds:
	docker-compose ${DOCKER_COMPOSE} exec ${CGDS} bash
gauge:
	docker-compose ${DOCKER_COMPOSE} exec gauge bash
test:
	docker-compose ${DOCKER_COMPOSE} exec ${CGDS} bash -c "cd ${HOME_IN_CONTAINER} && php artisan test"

install-local-db:
	cp -f .env.local-db .env
	make init up-local-db composer-local-db npm-install-local-db npm-dev-local-db
up-local-db:
	make init
	cp -f .env.local-db .env
	docker-compose ${DOCKER_COMPOSE_LOCAL_DB} up -d
up-local-db-build:
	make init
	cp -f .env.local-db .env
	docker-compose ${DOCKER_COMPOSE_LOCAL_DB} up -d --build
composer-local-db:
	docker-compose ${DOCKER_COMPOSE_LOCAL_DB} exec ${CGDS} bash -c "cd ${HOME_IN_CONTAINER} && composer install"
npm-install-local-db:
	docker-compose ${DOCKER_COMPOSE_LOCAL_DB} exec ${CGDS} bash -c "cd ${HOME_IN_CONTAINER} && npm install"
npm-dev-local-db:
	docker-compose ${DOCKER_COMPOSE_LOCAL_DB} exec ${CGDS} bash -c "cd ${HOME_IN_CONTAINER} && npm run dev"
balus-local-db:
	docker-compose ${DOCKER_COMPOSE_LOCAL_DB} down --rmi all --volumes --remove-orphans
