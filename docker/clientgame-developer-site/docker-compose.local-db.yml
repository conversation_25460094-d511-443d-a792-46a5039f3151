version: '3'
services:
  clientgame-developer-site:
    build:
      context: php-fpm
      dockerfile: Dockerfile
    container_name: clientgame-developer-site
    hostname: clientgame-developer-site
    environment:
      - APP_ENV=local-db
        XDEBUG_MODE=debug,develop
        XDEBUG_CONFIG=client_host=host.docker.internal client_port=9003

    volumes:
      - ${SRC_DIR}:/home/<USER>/clientgame-developer-site
      - ./php-fpm/php.ini:/usr/local/etc/php/conf.d/php.ini
      - ./php-fpm/supervisor:/etc/supervisor/conf.d:rw
      - ./php-fpm/php-xdebug.ini:/usr/local/etc/php/conf.d/php-xdebug.ini
    depends_on:
      - netgame-cstool-db
      - freegame-developer-db
      - freegame-db
      - freegame-report-db
      - freegame-sandbox-db
      - gameplayer-manage-db
      - gameplayer-db
      - sbx-gameplayer-manage-db
      - sbx-gameplayer-db
    networks:
      - app-network

  nginx:
    container_name: clientgame-developer-site-local
    hostname: clientgame-developer-site-local
    ports:
      - 8080:80
    build:
      context: nginx
      dockerfile: Dockerfile
    platform: ${PLATFORM_NGINX-}
    volumes:
      - ${SRC_DIR}/public:/home/<USER>/clientgame-developer-site/public
      - ${HOST_NGINX_VOLUME}:/var/log/nginx:rw
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf:rw
    depends_on:
      - clientgame-developer-site
    networks:
      - app-network

  netgame-cstool-db:
    image: docker-registry.devops.dmmga.me/database/stg/netgame_cstool:latest
    container_name: cgds-netgame-cstool-db
    platform: linux/amd64
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: netgame_cstool
    ports:
      - "3406:3306"
    networks:
      - app-network
    volumes:
      - netgame-cstool-db_volume:/var/lib/mysql

  freegame-developer-db:
    image: docker-registry.devops.dmmga.me/database/stg/freegame_developer:latest
    container_name: cgds-freegame-developer-db
    platform: linux/amd64
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: freegame_developer
    ports:
      - "3407:3306"
    networks:
      - app-network
    volumes:
      - freegame-developer-db_volume:/var/lib/mysql

  freegame-db:
    image: docker-registry.devops.dmmga.me/database/stg/freegame:latest
    container_name: cgds-freegame-db
    platform: linux/amd64
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: freegame
    ports:
      - "3408:3306"
    networks:
      - app-network
    volumes:
      - freegame-db_volume:/var/lib/mysql

  freegame-report-db:
    image: docker-registry.devops.dmmga.me/database/stg/freegame_report:latest
    container_name: cgds-freegame-report-db
    platform: linux/amd64
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: freegame_report
    ports:
      - "3409:3306"
    networks:
      - app-network
    volumes:
      - freegame-report-db_volume:/var/lib/mysql

  freegame-sandbox-db:
    image: docker-registry.devops.dmmga.me/database/stg/freegame_sandbox:latest
    container_name: cgds-freegame-sandbox-db
    platform: linux/amd64
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: freegame_sandbox
    ports:
      - "3410:3306"
    networks:
      - app-network
    volumes:
      - freegame-sandbox-db_volume:/var/lib/mysql

  gameplayer-manage-db:
    image: docker-registry.devops.dmmga.me/database/stg/gameplayer_manage:latest
    container_name: cgds-gameplayer-manage-db
    platform: linux/amd64
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: gameplayer_manage
    ports:
      - "3411:3306"
    networks:
      - app-network
    volumes:
      - gameplayer-manage-db_volume:/var/lib/mysql

  gameplayer-db:
    image: docker-registry.devops.dmmga.me/database/stg/gameplayer:latest
    platform: linux/amd64
    container_name: cgds-gameplayer-db
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: gameplayer
    ports:
      - "3412:3306"
    networks:
      - app-network
    volumes:
      - gameplayer-db_volume:/var/lib/mysql

  sbx-gameplayer-manage-db:
    image: docker-registry.devops.dmmga.me/database/stg/sbx_gameplayer_manage:latest
    platform: linux/amd64
    container_name: cgds-sbx-gameplayer-manage-db
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: sbx_gameplayer_manage
    ports:
      - "3413:3306"
    networks:
      - app-network
    volumes:
      - sbx-gameplayer-manage-db_volume:/var/lib/mysql

  sbx-gameplayer-db:
    image: docker-registry.devops.dmmga.me/database/stg/sbx_gameplayer:latest
    platform: linux/amd64
    container_name: cgds-sbx-gameplayer-db
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: sbx_gameplayer
    ports:
      - "3414:3306"
    networks:
      - app-network
    volumes:
      - sbx-gameplayer-db_volume:/var/lib/mysql

  gauge:
    container_name: gauge
    build:
      context: gauge
      dockerfile: Dockerfile
    platform: ${PLATFORM_GAUGE-}
    volumes:
      - ../../e2e-test:/home/<USER>
    tty: true
    depends_on:
      - nginx

networks:
  app-network:
    driver: bridge

volumes:
  freegame-sandbox-db_volume:
  freegame-db_volume:
  freegame-report-db_volume:
  freegame-developer-db_volume:
  sbx-gameplayer-db_volume:
  sbx-gameplayer-manage-db_volume:
  gameplayer-db_volume:
  gameplayer-manage-db_volume:
  netgame-cstool-db_volume:
